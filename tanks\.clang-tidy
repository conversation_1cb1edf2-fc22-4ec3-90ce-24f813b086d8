Checks: "-*,bugprone-*,cppcoreguidelines-*,misc-*,modernize-*,performance-*,readability-*,google-build-namespaces,google-build-using-namespace,google-global-names-in-headers,google-readability-casting,google-runtime-operator,-cppcoreguidelines-pro-bounds-array-to-pointer-decay,-cppcoreguidelines-pro-bounds-constant-array-index,-*magic-numbers*,-*avoid-c-arrays*,-modernize-use-trailing-return-type,-readability-else-after-return,-readability-named-parameter"
WarningsAsErrors: "*"
ExtraArgs:
  - "-std=c++17"
ExtraArgsBefore:
  - "-xc++"


---
events:
  -
    kind: "message-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.2+f32259642
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/6/5 10:24:44。
      节点 1 上的项目“F:\\linux\\tanks\\build\\CMakeFiles\\4.0.2\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> F:\\linux\\tanks\\build\\CMakeFiles\\4.0.2\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“F:\\linux\\tanks\\build\\CMakeFiles\\4.0.2\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:00.99
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        F:/linux/tanks/build/CMakeFiles/4.0.2/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.2+f32259642
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/6/5 10:24:45。
      节点 1 上的项目“F:\\linux\\tanks\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> F:\\linux\\tanks\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“F:\\linux\\tanks\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:00.83
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        F:/linux/tanks/build/CMakeFiles/4.0.2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-o1q7s6"
      binary: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-o1q7s6"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-o1q7s6'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_908f1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.2+f32259642
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/6/5 10:24:47。
        节点 1 上的项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1q7s6\\cmTC_908f1.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_908f1.dir\\Debug\\”。
          正在创建目录“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1q7s6\\Debug\\”。
          正在创建目录“cmTC_908f1.dir\\Debug\\cmTC_908f1.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_908f1.dir\\Debug\\cmTC_908f1.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_908f1.dir\\Debug\\\\" /Fd"cmTC_908f1.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\Program Files (x86)\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30157 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          CMakeCCompilerABI.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_908f1.dir\\Debug\\\\" /Fd"cmTC_908f1.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\Program Files (x86)\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1q7s6\\Debug\\cmTC_908f1.exe" /INCREMENTAL /ILK:"cmTC_908f1.dir\\Debug\\cmTC_908f1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-o1q7s6/Debug/cmTC_908f1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-o1q7s6/Debug/cmTC_908f1.lib" /MACHINE:X64  /machine:x64 cmTC_908f1.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_908f1.vcxproj -> F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1q7s6\\Debug\\cmTC_908f1.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_908f1.dir\\Debug\\cmTC_908f1.tlog\\unsuccessfulbuild”。
          正在对“cmTC_908f1.dir\\Debug\\cmTC_908f1.tlog\\cmTC_908f1.lastbuildstate”执行 Touch 任务。
        已完成生成项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o1q7s6\\cmTC_908f1.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:00.73
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30157.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-9ek5u8"
      binary: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-9ek5u8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-9ek5u8'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_a18b9.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.2+f32259642
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/6/5 10:24:49。
        节点 1 上的项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ek5u8\\cmTC_a18b9.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_a18b9.dir\\Debug\\”。
          正在创建目录“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ek5u8\\Debug\\”。
          正在创建目录“cmTC_a18b9.dir\\Debug\\cmTC_a18b9.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_a18b9.dir\\Debug\\cmTC_a18b9.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_a18b9.dir\\Debug\\\\" /Fd"cmTC_a18b9.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "F:\\Program Files (x86)\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30157 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          CMakeCXXCompilerABI.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_a18b9.dir\\Debug\\\\" /Fd"cmTC_a18b9.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "F:\\Program Files (x86)\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ek5u8\\Debug\\cmTC_a18b9.exe" /INCREMENTAL /ILK:"cmTC_a18b9.dir\\Debug\\cmTC_a18b9.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-9ek5u8/Debug/cmTC_a18b9.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-9ek5u8/Debug/cmTC_a18b9.lib" /MACHINE:X64  /machine:x64 cmTC_a18b9.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_a18b9.vcxproj -> F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ek5u8\\Debug\\cmTC_a18b9.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_a18b9.dir\\Debug\\cmTC_a18b9.tlog\\unsuccessfulbuild”。
          正在对“cmTC_a18b9.dir\\Debug\\cmTC_a18b9.tlog\\cmTC_a18b9.lastbuildstate”执行 Touch 任务。
        已完成生成项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ek5u8\\cmTC_a18b9.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:00.61
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30157.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:11 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-5nujhs"
      binary: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-5nujhs"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-5nujhs'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_08215.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.2+f32259642
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/6/5 10:24:50。
        节点 1 上的项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\cmTC_08215.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_08215.dir\\Debug\\”。
          正在创建目录“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\Debug\\”。
          正在创建目录“cmTC_08215.dir\\Debug\\cmTC_08215.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_08215.dir\\Debug\\cmTC_08215.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_08215.dir\\Debug\\\\" /Fd"cmTC_08215.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30157 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          src.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_08215.dir\\Debug\\\\" /Fd"cmTC_08215.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\src.c"
        F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\src.c(1,10): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory [F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\cmTC_08215.vcxproj]
        已完成生成项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\cmTC_08215.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\cmTC_08215.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\src.c(1,10): fatal error C1083: 无法打开包括文件: “pthread.h”: No such file or directory [F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-5nujhs\\cmTC_08215.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.43
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:11 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-gobv9p"
      binary: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-gobv9p"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-gobv9p'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_bbf9f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.2+f32259642
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/6/5 10:24:51。
        节点 1 上的项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\cmTC_bbf9f.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_bbf9f.dir\\Debug\\”。
          正在创建目录“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\Debug\\”。
          正在创建目录“cmTC_bbf9f.dir\\Debug\\cmTC_bbf9f.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_bbf9f.dir\\Debug\\cmTC_bbf9f.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bbf9f.dir\\Debug\\\\" /Fd"cmTC_bbf9f.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\CheckFunctionExists.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30157 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          CheckFunctionExists.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bbf9f.dir\\Debug\\\\" /Fd"cmTC_bbf9f.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\CheckFunctionExists.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\Debug\\cmTC_bbf9f.exe" /INCREMENTAL /ILK:"cmTC_bbf9f.dir\\Debug\\cmTC_bbf9f.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-gobv9p/Debug/cmTC_bbf9f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-gobv9p/Debug/cmTC_bbf9f.lib" /MACHINE:X64  /machine:x64 cmTC_bbf9f.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 无法打开文件“pthreads.lib” [F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\cmTC_bbf9f.vcxproj]
        已完成生成项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\cmTC_bbf9f.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\cmTC_bbf9f.vcxproj”(默认目标) (1) ->
        (Link 目标) -> 
          LINK : fatal error LNK1104: 无法打开文件“pthreads.lib” [F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gobv9p\\cmTC_bbf9f.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.49
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "F:/Program Files (x86)/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:11 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-q4b6d0"
      binary: "F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-q4b6d0"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-q4b6d0'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_fb6c5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.2+f32259642
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/6/5 10:24:52。
        节点 1 上的项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\cmTC_fb6c5.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_fb6c5.dir\\Debug\\”。
          正在创建目录“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\Debug\\”。
          正在创建目录“cmTC_fb6c5.dir\\Debug\\cmTC_fb6c5.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_fb6c5.dir\\Debug\\cmTC_fb6c5.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fb6c5.dir\\Debug\\\\" /Fd"cmTC_fb6c5.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\CheckFunctionExists.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30157 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          CheckFunctionExists.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fb6c5.dir\\Debug\\\\" /Fd"cmTC_fb6c5.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\CheckFunctionExists.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\Debug\\cmTC_fb6c5.exe" /INCREMENTAL /ILK:"cmTC_fb6c5.dir\\Debug\\cmTC_fb6c5.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-q4b6d0/Debug/cmTC_fb6c5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/linux/tanks/build/CMakeFiles/CMakeScratch/TryCompile-q4b6d0/Debug/cmTC_fb6c5.lib" /MACHINE:X64  /machine:x64 cmTC_fb6c5.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 无法打开文件“pthread.lib” [F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\cmTC_fb6c5.vcxproj]
        已完成生成项目“F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\cmTC_fb6c5.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\cmTC_fb6c5.vcxproj”(默认目标) (1) ->
        (Link 目标) -> 
          LINK : fatal error LNK1104: 无法打开文件“pthread.lib” [F:\\linux\\tanks\\build\\CMakeFiles\\CMakeScratch\\TryCompile-q4b6d0\\cmTC_fb6c5.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.51
        
      exitCode: 1
...

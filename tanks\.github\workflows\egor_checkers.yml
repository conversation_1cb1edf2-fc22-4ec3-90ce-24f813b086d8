name: github_ubuntu_checkers
on: [ push ]
jobs:
  clang-format:
    runs-on: ubuntu-20.04
    timeout-minutes: 1
    steps:
      - uses: actions/checkout@v3
      - run: clang-format-12 --version
      - run: for file in $(find . -iname '*.cpp' -or -iname '*.c' -or -iname '*.h' -and -not -iname 'doctest.h' ); do diff -u <(cat "$file") <(clang-format-12 "$file") || exit 1; done
  clang-tidy:
    runs-on: ubuntu-20.04
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v3
      - run: sudo apt-get update && sudo apt-get install clang-tidy-12 libsfml-dev libboost1.71-all-dev
      - run: clang-tidy-12 --version
      - run: ls
      - run: clang-tidy-12 --config-file='./.clang-tidy' $(find . -path '*/CMakeFiles/*' -prune -or \( -iname '*.cpp' -and -not -iname '*tests.cpp' -or -iname '*.h' -and -not -iname 'doctest.h' \) -print) -- -Iheaders
  cppcheck:
    runs-on: ubuntu-20.04
    timeout-minutes: 1
    steps:
      - uses: actions/checkout@v3
      - run: sudo apt-get update && sudo apt-get install cppcheck
      - run: cppcheck --version
      - run: cppcheck --language=c++ --check-config --check-library -Iheaders --enable=all -isystem -idoctest.h -imodel_tests.cpp --suppress=missingIncludeSystem --error-exitcode=1 --inline-suppr $(find . -iname '*.cpp')


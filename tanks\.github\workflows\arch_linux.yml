name: github_arch
on: [ push ]
jobs:
  arch-gcc-compile:
    runs-on: ubuntu-latest
    container: archlinux:latest

    steps:
      - uses: actions/checkout@v3
      - run: pacman -Syyu --noconfirm --noprogressbar
      - run: pacman -S cmake ninja gcc sfml boost boost-libs --noconfirm --noprogressbar sqlite
      - run: cmake . -GNinja
      - run: cmake --build . -t tanks -t model_tests
      


 
